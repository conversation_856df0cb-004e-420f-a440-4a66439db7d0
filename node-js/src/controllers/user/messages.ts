import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  sendMessageService,
  markMessageAsReadService,
} from "~/services/user/messages";
import {
  sendMessageBodySchema,
  markMessageAsReadParamsSchema,
} from "~/validators/user/messages";

/**
 * Send a message in a conversation
 */
async function sendMessage(request: Request, response: Response) {
  try {
    const validatedData = sendMessageBodySchema.parse(request.body);

    const { message } = await sendMessageService({
      ...validatedData,
      senderId: request.user.id,
    });

    return response.success(
      {
        data: { message },
      },
      {
        message: "Message sent successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Mark a message as read
 */
async function markMessageAsRead(request: Request, response: Response) {
  try {
    const { messageId } = markMessageAsReadParamsSchema.parse(request.params);

    const { message } = await markMessageAsReadService({
      messageId,
      userId: request.user.id,
    });

    return response.success(
      {
        data: { message },
      },
      {
        message: "Message marked as read",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { sendMessage, markMessageAsRead };
