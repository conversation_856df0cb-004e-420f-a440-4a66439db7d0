import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getConversationsService,
  getOrCreateConversationService,
  getMessagesService,
} from "~/services/user/conversations";
import {
  getConversationsQuerySchema,
  getOrCreateConversationBodySchema,
  getMessagesParamsSchema,
  getMessagesQuerySchema,
} from "~/validators/user/conversations";

/**
 * Get all conversations for the authenticated user
 */
async function getConversations(request: Request, response: Response) {
  try {
    const { search } = getConversationsQuerySchema.parse(request.query);

    const { conversations } = await getConversationsService({
      userId: request.user.id,
    });

    // Filter conversations by search if provided
    const filteredConversations = search
      ? conversations.filter((conversation) =>
          conversation.members.some((member) =>
            member.name.toLowerCase().includes(search.toLowerCase()),
          ),
        )
      : conversations;

    return response.success(
      {
        data: { conversations: filteredConversations },
      },
      {
        message: "Conversations retrieved successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get or create a conversation
 */
async function getOrCreateConversation(request: Request, response: Response) {
  try {
    const validatedData = getOrCreateConversationBodySchema.parse(request.body);

    const { conversation } = await getOrCreateConversationService({
      userId: request.user.id,
      ...validatedData,
    });

    return response.success(
      {
        data: { conversation },
      },
      {
        message: "Conversation retrieved successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get messages for a specific conversation
 */
async function getMessages(request: Request, response: Response) {
  try {
    const { conversationId } = getMessagesParamsSchema.parse(request.params);
    const { limit, offset } = getMessagesQuerySchema.parse(request.query);

    const { messages } = await getMessagesService({
      conversationId,
      userId: request.user.id,
      limit,
      offset,
    });

    return response.success(
      {
        data: { messages },
      },
      {
        message: "Messages retrieved successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getConversations, getOrCreateConversation, getMessages };
