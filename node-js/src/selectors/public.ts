const auth = {
  id: true,
  email: true,
  createdAt: true,
  updatedAt: true,
};

const category = {
  id: true,
  name: true,
  createdAt: true,
  updatedAt: true,
};

const product = {
  id: true,
  pictureIds: true,
  name: true,
  description: true,
  previousUsage: true,
  sku: true,
  stock: true,
  price: true,
  salePrice: true,
  condition: true,
  isVerified: true,
  createdAt: true,
  updatedAt: true,
};

const orderToProduct = {
  id: true,
  quantity: true,
  createdAt: true,
  updatedAt: true,
};

const order = {
  id: true,
  totalPrice: true,
  status: true,
  deliveryOption: true,
  createdAt: true,
  updatedAt: true,
};

const deliveryRequest = {
  id: true,
  price: true,
  acceptedPrice: true,
  status: true,
  createdAt: true,
  updatedAt: true,
};

const logisticProviderResponse = {
  id: true,
  price: true,
  createdAt: true,
  updatedAt: true,
};

const review = {
  id: true,
  rating: true,
  comment: true,
  createdAt: true,
  updatedAt: true,
};

const member = {
  id: true,
  name: true,
  pictureId: true,
};

const conversation = {
  id: true,
  referenceId: true,
  endedBy: true,
  type: true,
  createdAt: true,
  updatedAt: true,
};

const message = {
  id: true,
  content: true,
  isRead: true,
  createdAt: true,
  updatedAt: true,
};

export const publicSelector = {
  auth,
  category,
  product,
  order,
  orderToProduct,
  deliveryRequest,
  logisticProviderResponse,
  review,
  member,
  conversation,
  message,
};
