import type { ConversationType } from "@prisma/client";

import { NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";

/**
 * Get all conversations for a user
 */
async function getConversationsService({ userId }: { userId: string }) {
  const conversations = await prisma.conversation.findMany({
    where: {
      members: {
        some: {
          authId: userId,
        },
      },
    },
    select: {
      ...publicSelector.conversation,
      members: {
        select: {
          auth: {
            select: {
              id: true,
              adminProfile: {
                select: publicSelector.member,
              },
              vendorProfile: {
                select: publicSelector.member,
              },
              logisticProviderProfile: {
                select: publicSelector.member,
              },
              userProfile: {
                select: publicSelector.member,
              },
            },
          },
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  // Transform the data to match frontend expectations
  const transformedConversations = conversations.map((conversation) => ({
    ...conversation,
    members: conversation.members.map((member) => {
      const auth = member.auth;
      const profile =
        auth.adminProfile ||
        auth.vendorProfile ||
        auth.logisticProviderProfile ||
        auth.userProfile;

      return {
        id: auth.id,
        name: profile?.name || "Unknown User",
        pictureId: profile?.pictureId || "",
      };
    }),
  }));

  return { conversations: transformedConversations };
}

/**
 * Get or create a conversation
 */
async function getOrCreateConversationService({
  userId,
  type,
  referenceId,
  memberAuthId,
}: {
  userId: string;
  type: ConversationType;
  referenceId?: string;
  memberAuthId: string;
}) {
  // First try to find existing conversation
  let conversation = await prisma.conversation.findFirst({
    where: {
      type,
      referenceId,
      AND: [
        { members: { some: { authId: userId } } },
        { members: { some: { authId: memberAuthId } } },
      ],
    },
    select: {
      ...publicSelector.conversation,
      members: {
        select: {
          auth: {
            select: {
              id: true,
              adminProfile: {
                select: publicSelector.member,
              },
              vendorProfile: {
                select: publicSelector.member,
              },
              logisticProviderProfile: {
                select: publicSelector.member,
              },
              userProfile: {
                select: publicSelector.member,
              },
            },
          },
        },
      },
    },
  });

  if (!conversation) {
    // Create new conversation
    const newConversation = await prisma.conversation.create({
      data: {
        type,
        referenceId,
      },
      select: {
        ...publicSelector.conversation,
      },
    });

    // Add members to conversation
    await prisma.conversationToAuth.createMany({
      data: [
        {
          conversationId: newConversation.id,
          authId: userId,
        },
        {
          conversationId: newConversation.id,
          authId: memberAuthId,
        },
      ],
    });

    // Create initial system message if referenceId exists
    if (referenceId) {
      const forType =
        type === "VENDOR"
          ? "for order"
          : type === "LOGISTIC"
            ? "for delivery"
            : "";

      await prisma.message.create({
        data: {
          content: `Conversation created ${forType} with ID ${referenceId}`,
          conversationId: newConversation.id,
          senderId: userId,
        },
      });
    }

    // Fetch the complete conversation with members
    conversation = await prisma.conversation.findUnique({
      where: { id: newConversation.id },
      select: {
        ...publicSelector.conversation,
        members: {
          select: {
            auth: {
              select: {
                id: true,
                adminProfile: {
                  select: publicSelector.member,
                },
                vendorProfile: {
                  select: publicSelector.member,
                },
                logisticProviderProfile: {
                  select: publicSelector.member,
                },
                userProfile: {
                  select: publicSelector.member,
                },
              },
            },
          },
        },
      },
    });
  }

  if (!conversation) {
    throw new NotFoundResponse("Conversation not found");
  }

  // Transform the data to match frontend expectations
  const transformedConversation = {
    ...conversation,
    members: conversation.members.map((member) => {
      const auth = member.auth;
      const profile =
        auth.adminProfile ||
        auth.vendorProfile ||
        auth.logisticProviderProfile ||
        auth.userProfile;

      return {
        id: auth.id,
        name: profile?.name || "Unknown User",
        pictureId: profile?.pictureId || "",
      };
    }),
  };

  return { conversation: transformedConversation };
}

/**
 * Get messages for a conversation
 */
async function getMessagesService({
  conversationId,
  userId,
  limit = 50,
  offset = 0,
}: {
  conversationId: string;
  userId: string;
  limit?: number;
  offset?: number;
}) {
  // First verify user is a member of the conversation
  const conversation = await prisma.conversation.findFirst({
    where: {
      id: conversationId,
      members: {
        some: {
          authId: userId,
        },
      },
    },
  });

  if (!conversation) {
    throw new NotFoundResponse("Conversation not found or access denied");
  }

  const messages = await prisma.message.findMany({
    where: {
      conversationId,
    },
    select: {
      ...publicSelector.message,
      sender: {
        select: {
          id: true,
          adminProfile: {
            select: publicSelector.member,
          },
          vendorProfile: {
            select: publicSelector.member,
          },
          logisticProviderProfile: {
            select: publicSelector.member,
          },
          userProfile: {
            select: publicSelector.member,
          },
        },
      },
    },
    orderBy: {
      createdAt: "asc",
    },
    take: limit,
    skip: offset,
  });

  // Transform the data to match frontend expectations
  const transformedMessages = messages.map((message) => {
    const profile =
      message.sender.adminProfile ||
      message.sender.vendorProfile ||
      message.sender.logisticProviderProfile ||
      message.sender.userProfile;

    return {
      ...message,
      sender: {
        id: message.sender.id,
        name: profile?.name || "Unknown User",
        pictureId: profile?.pictureId || "",
      },
    };
  });

  return { messages: transformedMessages };
}

export {
  getConversationsService,
  getOrCreateConversationService,
  getMessagesService,
};
