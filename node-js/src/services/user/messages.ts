import { NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";

/**
 * Send a message in a conversation
 */
async function sendMessageService({
  conversationId,
  content,
  senderId,
}: {
  conversationId: string;
  content: string;
  senderId: string;
}) {
  // First verify user is a member of the conversation
  const conversation = await prisma.conversation.findFirst({
    where: {
      id: conversationId,
      members: {
        some: {
          authId: senderId,
        },
      },
    },
  });

  if (!conversation) {
    throw new NotFoundResponse("Conversation not found or access denied");
  }

  // Check if conversation is ended
  if (conversation.endedBy) {
    throw new Error("Cannot send message to ended conversation");
  }

  // Create the message
  const message = await prisma.message.create({
    data: {
      content,
      conversationId,
      senderId,
    },
    select: {
      ...publicSelector.message,
      sender: {
        select: {
          id: true,
          adminProfile: {
            select: publicSelector.member,
          },
          vendorProfile: {
            select: publicSelector.member,
          },
          logisticProviderProfile: {
            select: publicSelector.member,
          },
          userProfile: {
            select: publicSelector.member,
          },
        },
      },
    },
  });

  // Update conversation's updatedAt timestamp
  await prisma.conversation.update({
    where: { id: conversationId },
    data: { updatedAt: new Date() },
  });

  // Transform the data to match frontend expectations
  const profile =
    message.sender.adminProfile ||
    message.sender.vendorProfile ||
    message.sender.logisticProviderProfile ||
    message.sender.userProfile;

  const transformedMessage = {
    ...message,
    sender: {
      id: message.sender.id,
      name: profile?.name || "Unknown User",
      pictureId: profile?.pictureId || "",
    },
  };

  return { message: transformedMessage };
}

/**
 * Mark a message as read
 */
async function markMessageAsReadService({
  messageId,
  userId,
}: {
  messageId: string;
  userId: string;
}) {
  // First verify the message exists and user has access
  const message = await prisma.message.findFirst({
    where: {
      id: messageId,
      conversation: {
        members: {
          some: {
            authId: userId,
          },
        },
      },
    },
  });

  if (!message) {
    throw new NotFoundResponse("Message not found or access denied");
  }

  // Update the message
  const updatedMessage = await prisma.message.update({
    where: { id: messageId },
    data: { isRead: true },
    select: {
      ...publicSelector.message,
      sender: {
        select: {
          id: true,
          adminProfile: {
            select: publicSelector.member,
          },
          vendorProfile: {
            select: publicSelector.member,
          },
          logisticProviderProfile: {
            select: publicSelector.member,
          },
          userProfile: {
            select: publicSelector.member,
          },
        },
      },
    },
  });

  // Transform the data to match frontend expectations
  const profile =
    updatedMessage.sender.adminProfile ||
    updatedMessage.sender.vendorProfile ||
    updatedMessage.sender.logisticProviderProfile ||
    updatedMessage.sender.userProfile;

  const transformedMessage = {
    ...updatedMessage,
    sender: {
      id: updatedMessage.sender.id,
      name: profile?.name || "Unknown User",
      pictureId: profile?.pictureId || "",
    },
  };

  return { message: transformedMessage };
}

/**
 * Get conversation members (for socket room management)
 */
async function getConversationMembersService({
  conversationId,
}: {
  conversationId: string;
}) {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    select: {
      members: {
        select: {
          authId: true,
        },
      },
    },
  });

  if (!conversation) {
    throw new NotFoundResponse("Conversation not found");
  }

  return {
    memberIds: conversation.members.map((member) => member.authId),
  };
}

export {
  sendMessageService,
  markMessageAsReadService,
  getConversationMembersService,
};
