import { z } from "zod";

export const getConversationsQuerySchema = z.object({
  search: z.string().optional(),
});

export const getOrCreateConversationBodySchema = z.object({
  type: z.enum(["VENDOR", "LOGISTIC"]),
  referenceId: z.string().optional(),
  memberAuthId: z.string(),
});

export const getMessagesParamsSchema = z.object({
  conversationId: z.string(),
});

export const getMessagesQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(50),
  offset: z.coerce.number().min(0).default(0),
});
