import type { ConversationType } from "@prisma/client";
import type { Server as SocketServer } from "socket.io";

import type { AuthenticatedSocket } from "~/socket";

import { events } from "~/lib/events";
import {
  getConversationsService,
  getOrCreateConversationService,
  getMessagesService,
} from "~/services/user/conversations";
import {
  sendMessageService,
  markMessageAsReadService,
  getConversationMembersService,
} from "~/services/user/messages";

async function handleRetrieveConversations({
  io,
  socket,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversations retrieved for: ", { user: userAuth });

    const { conversations } = await getConversationsService({
      userId: userAuth.id,
    });

    // Join user to their own room for notifications
    socket.join(userAuth.id);

    // Emit conversations back to the client
    socket.emit(events.conversations.get, {
      success: true,
      data: { conversations },
      message: "Conversations retrieved successfully",
    });

    console.log("Conversations retrieved", conversations.length);
  } catch (error) {
    console.error("Error retrieving conversations:", error);
    socket.emit(events.conversations.get, {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to retrieve conversations",
    });
  }
}

async function handleDetailsConversations({
  io,
  socket,
  type,
  referenceId,
  memberAuthId,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  type: ConversationType;
  referenceId?: string;
  memberAuthId: string;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversation details for: ", {
      type,
      referenceId,
      userAuthId: userAuth.id,
      memberAuthId,
    });

    const { conversation } = await getOrCreateConversationService({
      userId: userAuth.id,
      type,
      referenceId,
      memberAuthId,
    });

    // Join the conversation room
    socket.join(conversation.id);

    // Get messages for this conversation
    const { messages } = await getMessagesService({
      conversationId: conversation.id,
      userId: userAuth.id,
    });

    // Emit conversation details and messages back to the client
    socket.emit(events.conversations.send, {
      success: true,
      data: { conversation, messages },
      message: "Conversation details retrieved successfully",
    });

    console.log("Conversation details retrieved", conversation.id);
  } catch (error) {
    console.error("Error retrieving conversation details:", error);
    socket.emit(events.conversations.send, {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to retrieve conversation details",
    });
  }
}

async function handleSendMessages({
  io,
  socket,
  conversationId,
  content,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  conversationId: string;
  content: string;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Message sent for: ", { conversationId, content });

    const { message } = await sendMessageService({
      conversationId,
      content,
      senderId: userAuth.id,
    });

    // Get conversation members for broadcasting
    const { memberIds } = await getConversationMembersService({
      conversationId,
    });

    // Broadcast the new message to all conversation members
    for (const memberId of memberIds) {
      io.to(memberId).emit(events.messages.new, {
        success: true,
        data: { message, conversationId },
        message: "New message received",
      });
    }

    // Also emit to the conversation room
    io.to(conversationId).emit(events.messages.new, {
      success: true,
      data: { message, conversationId },
      message: "New message received",
    });

    console.log("Message sent and broadcasted", message.id);
  } catch (error) {
    console.error("Error sending message:", error);
    socket.emit(events.messages.send, {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send message",
    });
  }
}

async function handleReadMessages({
  io,
  socket,
  messageId,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  messageId: string;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Message read for: ", { messageId });

    const { message } = await markMessageAsReadService({
      messageId,
      userId: userAuth.id,
    });

    // Emit success response back to the client
    socket.emit(events.messages.read, {
      success: true,
      data: { message },
      message: "Message marked as read",
    });

    console.log("Message marked as read", message.id);
  } catch (error) {
    console.error("Error marking message as read:", error);
    socket.emit(events.messages.read, {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to mark message as read",
    });
  }
}

export {
  handleRetrieveConversations,
  handleDetailsConversations,
  handleSendMessages,
  handleReadMessages,
};
