import type { Server as HttpServer } from "node:http";

import type { ConversationType, Role } from "@prisma/client";
import type { Socket } from "socket.io";

import { Server as SocketServer } from "socket.io";

import { events } from "~/lib/events";
import { verifyRequest } from "~/middlewares/auth";
import { expandSocketResponse } from "~/middlewares/response";
import {
  handleDetailsConversations,
  handleReadMessages,
  handleRetrieveConversations,
  handleSendMessages,
} from "./events/conversations";

export type AuthenticatedSocket = Socket & {
  request: {
    user: {
      id: string;
      email: string;
      status: string;
      role: Role;
      isVerified: boolean;
      isDeleted: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
  };
};

function setupSocket(server: HttpServer) {
  const io = new SocketServer(server, {
    cors: {
      origin: "*",
    },
  });

  io.engine.use(expandSocketResponse);
  // @ts-ignore
  io.engine.use((request, response, next) => {
    const isHandshake = request._query.sid === undefined;
    if (!isHandshake) {
      return next();
    }

    verifyRequest({
      isVerified: true,
      isDeleted: false,
      allowedTypes: ["ACCESS"],
      allowedStatus: ["APPROVED"],
      allowedRoles: ["SUPER_ADMIN", "ADMIN", "LOGISTIC", "VENDOR", "USER"],
    })(request, response, next);
  });

  io.on(events.app.connection, (socket: AuthenticatedSocket) => {
    console.log("Connected");

    const userAuth = socket.request.user;

    console.log("User", userAuth);

    socket.on(events.conversations.retrieve, async () => {
      await handleRetrieveConversations({ io, socket });
    });

    socket.on(
      events.conversations.details,
      async ({
        type,
        referenceId,
        memberAuthId,
      }: {
        type: ConversationType;
        referenceId?: string;
        memberAuthId: string;
      }) => {
        await handleDetailsConversations({
          io,
          socket,
          type,
          referenceId,
          memberAuthId,
        });
      }
    );

    socket.on(
      events.messages.send,
      async ({
        conversationId,
        content,
      }: {
        conversationId: string;
        content: string;
      }) => {
        await handleSendMessages({
          io,
          socket,
          conversationId,
          content,
        });
      }
    );

    socket.on(
      events.messages.read,
      async ({ messageId }: { messageId: string }) => {
        await handleReadMessages({
          io,
          socket,
          messageId,
        });
      }
    );

    socket.on(events.app.disconnect, () => {
      console.log("Disconnected");
    });
  });
}

export { setupSocket };
